<?php


namespace app\admin\controller;


use cmf\controller\HomeBaseController;
use think\Controller;
use think\Db;
use app\user\model\UserModel;
use think\facade\Cache;
use MingYuanYun\AppStore\Client;
class TestController extends HomeBaseController
{
	public function initialize()
    {
        die;
        parent::initialize(); // TODO: Change the autogenerated stub
        header('Access-Control-Allow-Origin: *');
        header("Access-Control-Allow-Headers: token, Origin, X-Requested-With, Content-Type, Accept, Authorization");
        header('Access-Control-Allow-Methods: POST,GET,PUT,DELETE');

        if(request()->isOptions()){
            exit();
        }
    }
    public function test1(){
        include PLUGINS_PATH . "/ipaphp/vendor/autoload.php";
        include PLUGINS_PATH . "/ipaphp/vendor/yunchuang/appstore-connect-api/src/Client.php";

        $config = [
            'iss'    => '4286ce3a-c6c1-4d13-8aef-be13bb460e25',
            'kid'    => 'F8PC85BS5K',
            'secret' => './ios_test_c/4286ce3a-c6c1-4d13-8aef-be13bb460e25/4286ce3a-c6c1-4d13-8aef-be13bb460e25.p8'
        ];

        $client = new Client($config);

        $client->setHeaders([
            'Authorization' => 'Bearer ' . $client->getToken(),
        ]);
       
        $device_info = $client->api('device')->all([]);
		dump(count($device_info['data']));exit;
       // dump($device_info);exit;
        if(!(isset($device_info['data']) && $device_info['data'])){
            $device_info =  $client->api('certificates')->reg();
			dump($device_info);

            $device_info = $device_info['data'];
        }else{
            $device_info = $device_info['data'][0];
        }

       // dump($device_info);
    }
    
    public function test2(){
    	
        //     $data = [
        //     		'mobile'=>13564652557,
        //     		'user_pass'=>123456,
        //     		'pid'=>1
        //     	];
        //     $register = new UserModel();
        //     $log = $register->registerMobile($data);
        //     switch ($log) {
        //         case 0:
        //             $this->success('注册成功', url('AdminIndex/index'));
        //             break;
        //         case 1:
        //             $this->error("您的账户已注册过");
        //             break;
        //         case 2:
        //             $this->error("您输入的账号格式错误");
        //             break;
        //         default :
        //             $this->error('未受理的请求');
        //     }
       
        // $domain = Db::name('domain')->select();
        // $this->assign('domain',$domain);
        // return $this->fetch();
    }
    public function test3(){
		dump(Cache::get('test'));
    }
    
    
    public function cert(){
    	require_once PLUGINS_PATH . "/ipaphp/vendor/autoload.php";
        require_once PLUGINS_PATH . "/ipaphp/vendor/yunchuang/appstore-connect-api/src/Client.php";
    	if(!isset($count)){
            static $count =0;
        }
        if($count>=10){
          //  k_abort(500,'证书错误');
          exit;
        }
        // $udId_log = db('ios_udid_list')->where('udid',$udid)->where('app_id',$app_id)->find();
        $certificate_record=false;
        $certificate_record = Db::name('ios_certificate')->where('status',401)->find();


        

        $config = [
            'iss'    => $certificate_record['iss'],
            'kid'    => $certificate_record['kid'],
            'secret' => APP_ROOT . $certificate_record['p8_file']
        ];

        $client = new Client($config);

        $client->setHeaders([
            'Authorization' => 'Bearer ' . $client->getToken(),
        ]);

        //构建Bundle ID
       
			
        $result = $client->api('certificates')->all([]);
        

        if(isset($result['errors'][0]['status']) && $result['errors'][0]['status'] == 403){
          // Db::name('ios_certificate')->where('id',$certificate_record['id'])->update(['status'=>403]);
            $count++;
            echo "第{$count}次,错误码：403<br/>";
            return $this->cert();
        }elseif(isset($result['errors'][0]['status']) && $result['errors'][0]['status'] == 401){
          // Db::name('ios_certificate')->where('id',$certificate_record['id'])->update(['status'=>401]);
            $count++;
            echo "第{$count}次,错误码：401<br/>";
            return $this->cert();
        }
        return json([]);
    }

    public function test(){

        include PLUGINS_PATH . "/ipaphp/vendor/autoload.php";
        include PLUGINS_PATH . "/ipaphp/vendor/yunchuang/appstore-connect-api/src/Client.php";

        $iss = input('param.iss');
        $kid = input('param.kid');
        $user_id = input('param.user_id');
        $mark = trim(input('param.mark'));
        $p12_pwd = input('param.p12_pwd');

        $p8_file = request()->file('p8_file');
        if (!$p8_file) {
            $this->error('请上传p8文件！');
            exit;
        }
        if ($p8_file) {
            $p8_info = $p8_file->validate(['size' => 15678, 'ext' => 'p8'])->move(ROOT_PATH . 'public' . DS . 'ios_test');
            if ($p8_info) {
                // 成功上传后 获取上传信息
                $p8_file_path = DS . 'ios_test' . DS .$p8_info->getSaveName();
            } else {
                // 上传失败获取错误信息
                $this->error($p8_info->getError());
                exit;
            }
        }

        $config = [
            'iss'    => $iss,
            'kid'    => $kid,
            'secret' => $p8_file_path
        ];
        $client = new Client($config);

        $client->setHeaders([
            'Authorization' => 'Bearer ' . $client->getToken(),
        ]);

        $device_info  = $client->api('certificates')->all([]);
        if(!(isset($device_info['data']) && $device_info['data'])){
            $device_info =  $client->api('certificates')->reg();
        }
        $record = db('ios_certificate')->where('tid', $device_info['data'][0]['id'])->find();
        if ($record) {
            $this->error('该证书已存在！');
            exit;
        }
        file_put_contents("./ios_test/ios_development.cer", base64_decode($device_info['data'][0]['attributes']['certificateContent']));
        //openssl pkcs12 -export -inkey ios.key -in ios_development.pem -out ios_development.p12 -passout pass:\"123456\";
        $path = APP_ROOT.'/ios_test/';
        $output=[];
        $return_var='';
        $p12_name = md5(make_password(8).time()).'.p12';
        exec('openssl x509 -in '.$path.'ios_development.cer -inform DER -outform PEM -out '.$path.'ios_development.pem 2>&1',$output,$return_var);
        exec('openssl pkcs12 -export -inkey '.$path.'ios.key -in '.$path.'ios_development.pem -out '.$path.$p12_name.' -passout pass:\"'.$p12_pwd.'\" 2>&1',$output,$return_var);
        $data = [
            'type' => 1,
            'user_id' => $user_id,
            'iss' => $iss,
            'kid' => $kid,
            'tid' => $device_info['data'][0]['id'],
            'p12_pwd' => $p12_pwd,
            'create_time' => time(),
            'mark' => $mark,
            'p12_file'=>DS . 'ios_test' . DS .$p12_name,
            'p8_file'=>$p8_file_path
        ];
        db('ios_certificate')->insert($data);
        $this->success('添加成功！');
    }
}
